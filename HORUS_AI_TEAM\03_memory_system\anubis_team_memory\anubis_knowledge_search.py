#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 محرك البحث الذكي في المعرفة - فريق أنوبيس
Anubis AI Team Knowledge Search Engine
"""

import json
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from collections import defaultdict
import difflib

class AnubisKnowledgeSearch:
    """محرك البحث الذكي في معرفة فريق أنوبيس"""
    
    def __init__(self, memory_manager):
        self.memory_manager = memory_manager
        self.memory_root = memory_manager.memory_root
        
        # فهارس البحث
        self.experience_index = {}
        self.keyword_index = defaultdict(list)
        self.model_index = defaultdict(list)
        self.task_type_index = defaultdict(list)
        
        # بناء الفهارس
        self.build_search_indexes()
    
    def search_experiences(self, query: str, filters: Dict = None, limit: int = 10) -> List[Dict]:
        """البحث في التجارب"""
        
        print(f"🔍 البحث عن: {query}")
        
        # تنظيف وتحليل الاستعلام
        processed_query = self.process_search_query(query)
        
        # البحث في الفهارس
        search_results = []
        
        # البحث النصي
        text_results = self.text_search(processed_query)
        search_results.extend(text_results)
        
        # البحث بالكلمات المفتاحية
        keyword_results = self.keyword_search(processed_query)
        search_results.extend(keyword_results)
        
        # البحث الدلالي (مبسط)
        semantic_results = self.semantic_search(processed_query)
        search_results.extend(semantic_results)
        
        # إزالة التكرار وترتيب النتائج
        unique_results = self.deduplicate_and_rank(search_results, processed_query)
        
        # تطبيق المرشحات
        if filters:
            unique_results = self.apply_filters(unique_results, filters)
        
        # إضافة معلومات إضافية للنتائج
        enriched_results = self.enrich_search_results(unique_results[:limit])
        
        return enriched_results
    
    def find_similar_solutions(self, problem_description: str, task_type: str = None) -> List[Dict]:
        """العثور على حلول مشابهة"""
        
        print(f"💡 البحث عن حلول مشابهة لـ: {problem_description}")
        
        # تحليل وصف المشكلة
        problem_keywords = self.extract_keywords(problem_description)
        
        # البحث في التجارب الناجحة
        successful_experiences = self.get_successful_experiences(task_type)
        
        similar_solutions = []
        
        for experience in successful_experiences:
            # حساب درجة التشابه
            similarity_score = self.calculate_solution_similarity(
                problem_description, experience, problem_keywords
            )
            
            if similarity_score > 0.3:  # حد أدنى للتشابه
                solution = {
                    "experience_id": experience.get("id"),
                    "similarity_score": similarity_score,
                    "solution_summary": self.extract_solution_summary(experience),
                    "success_score": experience.get("success_score", 0),
                    "models_used": self.extract_models_used(experience),
                    "execution_time": self.calculate_total_execution_time(experience),
                    "task_type": experience.get("task_data", {}).get("task_type"),
                    "timestamp": experience.get("timestamp")
                }
                similar_solutions.append(solution)
        
        # ترتيب حسب التشابه والنجاح
        similar_solutions.sort(
            key=lambda x: (x["similarity_score"] * 0.7 + x["success_score"] * 0.3),
            reverse=True
        )
        
        return similar_solutions[:10]
    
    def recommend_team_composition(self, task_requirements: Dict) -> Dict:
        """توصية تشكيل الفريق"""
        
        print(f"🤖 توصية تشكيل الفريق للمتطلبات: {task_requirements}")
        
        task_type = task_requirements.get("task_type", "general")
        complexity = task_requirements.get("complexity", "medium")
        urgency = task_requirements.get("urgency", "normal")
        
        # تحليل التجارب المشابهة
        similar_experiences = self.find_experiences_by_requirements(task_requirements)
        
        # تحليل أداء النماذج
        model_performance = self.analyze_model_performance_for_requirements(task_requirements)
        
        # حساب التشكيل الأمثل
        optimal_composition = self.calculate_optimal_team_composition(
            task_type, complexity, urgency, similar_experiences, model_performance
        )
        
        recommendation = {
            "task_requirements": task_requirements,
            "recommended_team": optimal_composition["primary_team"],
            "alternative_teams": optimal_composition["alternatives"],
            "confidence_score": optimal_composition["confidence"],
            "expected_performance": optimal_composition["expected_performance"],
            "reasoning": optimal_composition["reasoning"],
            "similar_experiences_count": len(similar_experiences),
            "recommendation_timestamp": datetime.now().isoformat()
        }
        
        return recommendation
    
    def suggest_optimization_strategies(self, current_performance: Dict = None) -> List[Dict]:
        """اقتراح استراتيجيات التحسين"""
        
        print("⚡ اقتراح استراتيجيات التحسين...")
        
        # تحليل الأداء الحالي
        performance_analysis = self.analyze_current_performance(current_performance)
        
        # تحديد نقاط التحسين
        improvement_areas = self.identify_improvement_areas(performance_analysis)
        
        optimization_strategies = []
        
        for area in improvement_areas:
            strategies = self.generate_optimization_strategies_for_area(area)
            optimization_strategies.extend(strategies)
        
        # ترتيب الاستراتيجيات حسب الأولوية والتأثير
        optimization_strategies.sort(
            key=lambda x: (x.get("priority", 0) * 0.6 + x.get("impact", 0) * 0.4),
            reverse=True
        )
        
        return optimization_strategies[:15]
    
    def build_search_indexes(self):
        """بناء فهارس البحث"""
        
        print("📚 بناء فهارس البحث...")
        
        # فهرسة جميع التجارب
        for task_dir in self.memory_manager.experiences_path.iterdir():
            if task_dir.is_dir():
                for exp_file in task_dir.glob("experience_*.json"):
                    try:
                        with open(exp_file, 'r', encoding='utf-8') as f:
                            experience = json.load(f)
                        
                        exp_id = experience.get("id")
                        if exp_id:
                            # فهرس التجارب
                            self.experience_index[exp_id] = experience
                            
                            # فهرس الكلمات المفتاحية
                            keywords = self.extract_keywords_from_experience(experience)
                            for keyword in keywords:
                                self.keyword_index[keyword.lower()].append(exp_id)
                            
                            # فهرس النماذج
                            models = self.extract_models_used(experience)
                            for model in models:
                                self.model_index[model].append(exp_id)
                            
                            # فهرس أنواع المهام
                            task_type = experience.get("task_data", {}).get("task_type", "unknown")
                            self.task_type_index[task_type].append(exp_id)
                            
                    except Exception as e:
                        print(f"⚠️ خطأ في فهرسة {exp_file}: {e}")
        
        print(f"✅ تم فهرسة {len(self.experience_index)} تجربة")
    
    def process_search_query(self, query: str) -> Dict:
        """معالجة استعلام البحث"""
        
        processed = {
            "original_query": query,
            "cleaned_query": query.strip().lower(),
            "keywords": [],
            "models": [],
            "task_types": [],
            "operators": []
        }
        
        # استخراج الكلمات المفتاحية
        words = re.findall(r'\b\w+\b', query.lower())
        processed["keywords"] = [word for word in words if len(word) > 2]
        
        # البحث عن أسماء النماذج
        model_names = ["phi3", "mistral", "llama3", "strikegpt", "qwen", "gemini"]
        for model in model_names:
            if model in query.lower():
                processed["models"].append(model)
        
        # البحث عن أنواع المهام
        task_types = ["development", "analysis", "planning", "innovation", "review"]
        for task_type in task_types:
            if task_type in query.lower():
                processed["task_types"].append(task_type)
        
        return processed
    
    def text_search(self, processed_query: Dict) -> List[Dict]:
        """البحث النصي"""
        results = []
        
        for exp_id, experience in self.experience_index.items():
            # البحث في وصف المهمة
            task_description = experience.get("task_data", {}).get("task_description", "")
            
            # البحث في النتائج
            results_text = ""
            for result in experience.get("results", {}).get("execution_log", []):
                results_text += result.get("response", "") + " "
            
            # البحث في الدروس المستفادة
            lessons_text = " ".join(experience.get("lessons_learned", []))
            
            # حساب درجة التطابق
            search_text = f"{task_description} {results_text} {lessons_text}".lower()
            match_score = 0
            
            for keyword in processed_query["keywords"]:
                if keyword in search_text:
                    match_score += 1
            
            if match_score > 0:
                results.append({
                    "experience_id": exp_id,
                    "match_score": match_score / len(processed_query["keywords"]) if processed_query["keywords"] else 0,
                    "match_type": "text"
                })
        
        return results
    
    def keyword_search(self, processed_query: Dict) -> List[Dict]:
        """البحث بالكلمات المفتاحية"""
        results = []
        
        for keyword in processed_query["keywords"]:
            if keyword in self.keyword_index:
                for exp_id in self.keyword_index[keyword]:
                    results.append({
                        "experience_id": exp_id,
                        "match_score": 0.8,
                        "match_type": "keyword",
                        "matched_keyword": keyword
                    })
        
        return results
    
    def semantic_search(self, processed_query: Dict) -> List[Dict]:
        """البحث الدلالي (مبسط)"""
        results = []
        
        # البحث بالمرادفات والمفاهيم المشابهة
        synonyms = {
            "تطوير": ["development", "coding", "programming", "برمجة"],
            "تحليل": ["analysis", "review", "examination", "فحص"],
            "تخطيط": ["planning", "strategy", "استراتيجية"],
            "ابتكار": ["innovation", "creativity", "إبداع"],
            "مراجعة": ["review", "evaluation", "تقييم"]
        }
        
        for keyword in processed_query["keywords"]:
            for concept, related_terms in synonyms.items():
                if keyword in related_terms or any(term in keyword for term in related_terms):
                    # البحث عن التجارب المرتبطة بهذا المفهوم
                    for exp_id, experience in self.experience_index.items():
                        task_description = experience.get("task_data", {}).get("task_description", "").lower()
                        if any(term in task_description for term in related_terms):
                            results.append({
                                "experience_id": exp_id,
                                "match_score": 0.6,
                                "match_type": "semantic",
                                "concept": concept
                            })
        
        return results
    
    def deduplicate_and_rank(self, search_results: List[Dict], processed_query: Dict) -> List[Dict]:
        """إزالة التكرار وترتيب النتائج"""
        
        # تجميع النتائج حسب experience_id
        grouped_results = defaultdict(list)
        for result in search_results:
            grouped_results[result["experience_id"]].append(result)
        
        # حساب النتيجة المجمعة لكل تجربة
        ranked_results = []
        for exp_id, results in grouped_results.items():
            total_score = sum(r["match_score"] for r in results)
            match_types = [r["match_type"] for r in results]
            
            # تعزيز النتيجة إذا كان هناك تطابق من أنواع متعددة
            if len(set(match_types)) > 1:
                total_score *= 1.2
            
            # إضافة معلومات التجربة
            experience = self.experience_index[exp_id]
            
            ranked_results.append({
                "experience_id": exp_id,
                "total_score": total_score,
                "match_types": match_types,
                "experience": experience,
                "success_score": experience.get("success_score", 0),
                "timestamp": experience.get("timestamp")
            })
        
        # ترتيب حسب النتيجة المجمعة ونجاح التجربة
        ranked_results.sort(
            key=lambda x: (x["total_score"] * 0.7 + x["success_score"] * 0.3),
            reverse=True
        )
        
        return ranked_results
    
    def apply_filters(self, results: List[Dict], filters: Dict) -> List[Dict]:
        """تطبيق المرشحات"""
        filtered_results = results
        
        # مرشح نوع المهمة
        if "task_type" in filters:
            task_type = filters["task_type"]
            filtered_results = [
                r for r in filtered_results
                if r["experience"].get("task_data", {}).get("task_type") == task_type
            ]
        
        # مرشح النموذج
        if "model" in filters:
            model = filters["model"]
            filtered_results = [
                r for r in filtered_results
                if any(model in result.get("model", "") 
                      for result in r["experience"].get("results", {}).get("execution_log", []))
            ]
        
        # مرشح درجة النجاح
        if "min_success_score" in filters:
            min_score = filters["min_success_score"]
            filtered_results = [
                r for r in filtered_results
                if r["experience"].get("success_score", 0) >= min_score
            ]
        
        # مرشح التاريخ
        if "date_range" in filters:
            start_date = filters["date_range"].get("start")
            end_date = filters["date_range"].get("end")
            
            if start_date or end_date:
                filtered_results = [
                    r for r in filtered_results
                    if self.is_in_date_range(r["experience"].get("timestamp"), start_date, end_date)
                ]
        
        return filtered_results
    
    def enrich_search_results(self, results: List[Dict]) -> List[Dict]:
        """إثراء نتائج البحث بمعلومات إضافية"""
        enriched_results = []
        
        for result in results:
            experience = result["experience"]
            
            enriched_result = {
                "experience_id": result["experience_id"],
                "relevance_score": result["total_score"],
                "success_score": result["success_score"],
                "match_types": result["match_types"],
                "task_summary": {
                    "type": experience.get("task_data", {}).get("task_type"),
                    "description": experience.get("task_data", {}).get("task_description", "")[:200] + "...",
                    "priority": experience.get("task_data", {}).get("priority")
                },
                "execution_summary": {
                    "models_used": self.extract_models_used(experience),
                    "total_time": self.calculate_total_execution_time(experience),
                    "success_rate": self.calculate_phase_success_rate(experience)
                },
                "key_insights": experience.get("lessons_learned", [])[:3],
                "timestamp": experience.get("timestamp"),
                "similar_experiences_count": self.count_similar_experiences(experience)
            }
            
            enriched_results.append(enriched_result)
        
        return enriched_results
    
    def extract_keywords_from_experience(self, experience: Dict) -> List[str]:
        """استخراج الكلمات المفتاحية من التجربة"""
        keywords = []
        
        # من وصف المهمة
        task_description = experience.get("task_data", {}).get("task_description", "")
        keywords.extend(re.findall(r'\b\w+\b', task_description.lower()))
        
        # من الدروس المستفادة
        lessons = experience.get("lessons_learned", [])
        for lesson in lessons:
            keywords.extend(re.findall(r'\b\w+\b', lesson.lower()))
        
        # تنظيف الكلمات المفتاحية
        keywords = [k for k in keywords if len(k) > 2 and k not in ["the", "and", "or", "في", "من", "إلى"]]
        
        return list(set(keywords))
    
    def extract_models_used(self, experience: Dict) -> List[str]:
        """استخراج النماذج المستخدمة"""
        models = []
        for result in experience.get("results", {}).get("execution_log", []):
            model = result.get("model")
            if model:
                models.append(model)
        return list(set(models))
    
    def calculate_total_execution_time(self, experience: Dict) -> float:
        """حساب إجمالي وقت التنفيذ"""
        total_time = 0
        for result in experience.get("results", {}).get("execution_log", []):
            total_time += result.get("execution_time", 0)
        return total_time
    
    def calculate_phase_success_rate(self, experience: Dict) -> float:
        """حساب معدل نجاح المراحل"""
        execution_log = experience.get("results", {}).get("execution_log", [])
        if not execution_log:
            return 0.0
        
        successful_phases = len([r for r in execution_log if r.get("success", False)])
        return successful_phases / len(execution_log)
    
    def get_successful_experiences(self, task_type: str = None) -> List[Dict]:
        """الحصول على التجارب الناجحة"""
        successful_experiences = []
        
        for exp_id, experience in self.experience_index.items():
            if experience.get("success_score", 0) > 0.7:
                if task_type is None or experience.get("task_data", {}).get("task_type") == task_type:
                    successful_experiences.append(experience)
        
        return successful_experiences

def main():
    """دالة اختبار"""
    print("🔍 اختبار محرك البحث الذكي لفريق أنوبيس")
    print("=" * 50)
    
    print("⚠️ هذا المحرك يحتاج إلى memory_manager للعمل")
    print("استخدم: search_engine = AnubisKnowledgeSearch(memory_manager)")

if __name__ == "__main__":
    main()
