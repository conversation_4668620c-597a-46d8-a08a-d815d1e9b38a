#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مساعد التعاون مع الذكاء الاصطناعي - أنوبيس
Anubis AI Collaboration Helper
"""

import subprocess
import json
import time
from datetime import datetime
from pathlib import Path

class AnubisAICollaborationHelper:
    """مساعد التعاون مع فريق الذكاء الاصطناعي"""
    
    def __init__(self):
        self.selected_models = {
            "phi3:mini": "المحلل السريع",
            "mistral:7b": "المطور الخبير", 
            "llama3:8b": "المستشار الاستراتيجي"
        }
        
    def create_ollama_prompt(self, model_name, task_description, context=""):
        """إنشاء prompt لنموذج Ollama"""
        role_map = {
            "phi3:mini": "محلل سريع ومباشر",
            "mistral:7b": "مطور خبير ومتخصص",
            "llama3:8b": "مستشار استراتيجي وحكيم"
        }
        
        role = role_map.get(model_name, "مساعد ذكي")
        
        prompt = f"""🏺 مشروع أنوبيس - Universal AI Assistants

أنت {role} في فريق تطوير نظام المساعدين الذكيين.

📋 السياق:
{context if context else "نعمل على تطوير وتحسين نظام أنوبيس للمساعدين الذكيين"}

🎯 المهمة:
{task_description}

🔍 المطلوب منك (كـ{role}):
- تحليل المهمة من منظورك المتخصص
- اقتراح حل عملي ومفصل
- تحديد الخطوات اللازمة للتنفيذ
- تحديد أي مخاطر أو تحديات

⚡ معلومات تقنية مهمة:
- نستخدم Python, FastAPI, Docker
- النظام الأساسي على المنفذ 8000
- النظام المعزول على المنفذ 8080
- نركز على الأمان والعزل

🎯 اجعل إجابتك:
- واضحة ومباشرة
- عملية وقابلة للتنفيذ
- مدعومة بأمثلة الكود إذا لزم الأمر
- مناسبة لدورك كـ{role}

الرجاء تقديم حلك:"""
        
        return prompt
    
    def run_ollama_model(self, model_name, prompt):
        """تشغيل نموذج Ollama مع prompt"""
        try:
            print(f"🤖 استشارة {model_name}...")
            
            # تشغيل النموذج
            result = subprocess.run([
                'ollama', 'run', model_name, prompt
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                response = result.stdout.strip()
                print(f"✅ {model_name} أجاب بنجاح")
                return {
                    "model": model_name,
                    "success": True,
                    "response": response,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                print(f"❌ خطأ من {model_name}: {result.stderr}")
                return {
                    "model": model_name,
                    "success": False,
                    "error": result.stderr,
                    "timestamp": datetime.now().isoformat()
                }
                
        except subprocess.TimeoutExpired:
            print(f"⏰ انتهت مهلة {model_name}")
            return {
                "model": model_name,
                "success": False,
                "error": "timeout",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            print(f"❌ خطأ في {model_name}: {e}")
            return {
                "model": model_name,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def create_gemini_request_file(self, task_description, ollama_responses, context=""):
        """إنشاء ملف طلب لـ Gemini CLI"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"anubis_gemini_collaboration_request_{timestamp}.md"
        
        content = f"""# 🏺 طلب تعاون Gemini CLI - مشروع أنوبيس

## 📋 السياق:
{context if context else "نعمل على تطوير وتحسين نظام أنوبيس للمساعدين الذكيين"}

## 🎯 المهمة الأساسية:
{task_description}

## 🤖 استشارات النماذج المحلية:

"""
        
        for response in ollama_responses:
            if response["success"]:
                content += f"""### 📋 رأي {response['model']} ({self.selected_models.get(response['model'], 'مساعد')}):

```
{response['response'][:1000]}{'...' if len(response['response']) > 1000 else ''}
```

---

"""
            else:
                content += f"""### ❌ {response['model']} - فشل في الاستجابة:
خطأ: {response.get('error', 'غير محدد')}

---

"""
        
        content += f"""## 🎯 المطلوب من Gemini CLI:

### 1. مراجعة الاستشارات:
- تحليل آراء النماذج المحلية
- تحديد أفضل الاقتراحات
- دمج الحلول المختلفة

### 2. تقديم التوجيه النهائي:
- خطة عمل واضحة ومفصلة
- أولويات التنفيذ
- أفضل الممارسات

### 3. ضمان الجودة:
- مراجعة الحلول المقترحة
- تحديد المخاطر المحتملة
- اقتراح تحسينات

## 📊 معلومات تقنية:
- **المشروع:** Universal AI Assistants (Anubis)
- **التقنيات:** Python, FastAPI, Docker, PostgreSQL, Redis
- **النظام الأساسي:** Port 8000
- **النظام المعزول:** Port 8080
- **التركيز:** الأمان، العزل، الأداء

## 🎯 الهدف النهائي:
حل شامل ومدروس يجمع بين خبرة النماذج المحلية وحكمة Gemini CLI لتحقيق أفضل نتيجة ممكنة.

---

**🏺 شكراً لتعاونكم في تطوير نظام أنوبيس!**

*تم إنشاؤه في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"📄 تم إنشاء طلب Gemini: {filename}")
        return filename
    
    def collaborate_on_task(self, task_description, context="", use_models=None):
        """التعاون مع النماذج على مهمة محددة"""
        print("🏺 بدء التعاون مع فريق الذكاء الاصطناعي")
        print("=" * 60)
        print(f"🎯 المهمة: {task_description}")
        print(f"📋 السياق: {context}")
        
        # تحديد النماذج المستخدمة
        models_to_use = use_models or list(self.selected_models.keys())
        
        # جمع الاستشارات من النماذج المحلية
        ollama_responses = []
        
        for model_name in models_to_use:
            print(f"\n🤖 استشارة {model_name} ({self.selected_models.get(model_name, 'مساعد')})...")
            
            # إنشاء prompt مخصص
            prompt = self.create_ollama_prompt(model_name, task_description, context)
            
            # تشغيل النموذج
            response = self.run_ollama_model(model_name, prompt)
            ollama_responses.append(response)
            
            # انتظار قصير بين النماذج
            time.sleep(2)
        
        # إنشاء طلب Gemini
        print(f"\n🌟 إنشاء طلب تعاون لـ Gemini CLI...")
        gemini_file = self.create_gemini_request_file(task_description, ollama_responses, context)
        
        # حفظ النتائج
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"anubis_ai_collaboration_results_{timestamp}.json"
        
        results = {
            "task_description": task_description,
            "context": context,
            "models_used": models_to_use,
            "ollama_responses": ollama_responses,
            "gemini_request_file": gemini_file,
            "timestamp": timestamp
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 تم حفظ النتائج: {results_file}")
        
        # عرض الملخص
        successful_models = [r["model"] for r in ollama_responses if r["success"]]
        failed_models = [r["model"] for r in ollama_responses if not r["success"]]
        
        print(f"\n📊 ملخص التعاون:")
        print(f"✅ نماذج نجحت: {len(successful_models)}/{len(models_to_use)}")
        if successful_models:
            print(f"   {', '.join(successful_models)}")
        if failed_models:
            print(f"❌ نماذج فشلت: {', '.join(failed_models)}")
        
        print(f"\n🌟 الخطوة التالية:")
        print(f"1. راجع الملف: {gemini_file}")
        print(f"2. انسخ المحتوى إلى Gemini CLI أو https://gemini.google.com/")
        print(f"3. احصل على التوجيه النهائي من Gemini")
        print(f"4. نفذ الحل المدمج")
        
        return results

def main():
    """الدالة الرئيسية"""
    helper = AnubisAICollaborationHelper()
    
    print("🏺 مساعد التعاون مع الذكاء الاصطناعي - أنوبيس")
    print("=" * 60)
    
    # مثال على التعاون
    task = "تحسين نظام العزل المعزول وإضافة ميزة مراقبة متقدمة"
    context = "لدينا نظام معزول يعمل على المنفذ 8080 مع API وWorker وMonitor، نريد تحسينه وإضافة ميزات جديدة"
    
    results = helper.collaborate_on_task(task, context)
    
    return results

if __name__ == "__main__":
    main()
