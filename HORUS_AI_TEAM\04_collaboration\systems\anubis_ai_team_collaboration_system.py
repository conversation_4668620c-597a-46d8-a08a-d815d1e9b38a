#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام التعاون مع فريق الذكاء الاصطناعي - أنوبيس
Anubis AI Team Collaboration System
"""

import subprocess
import json
import time
from datetime import datetime
from pathlib import Path

class AnubisAITeam:
    """فريق الذكاء الاصطناعي المساعد"""
    
    def __init__(self):
        # اختيار أفضل النماذج للفريق
        self.ai_models = {
            "phi3:mini": {
                "role": "المحلل السريع",
                "specialty": "تحليل سريع وإجابات مباشرة",
                "use_case": "تحليل الكود، فحص الأخطاء، اقتراحات سريعة",
                "speed": "عالية جداً",
                "accuracy": "جيدة"
            },
            "mistral:7b": {
                "role": "المطور الخبير", 
                "specialty": "البرمجة والتطوير المتقدم",
                "use_case": "كتابة الكود، حل المشاكل التقنية، التصميم",
                "speed": "عالية",
                "accuracy": "ممتازة"
            },
            "llama3:8b": {
                "role": "المستشار الاستراتيجي",
                "specialty": "التخطيط والاستراتيجية",
                "use_case": "تخطيط المشاريع، اتخاذ القرارات، التوجيه",
                "speed": "متوسطة",
                "accuracy": "ممتازة جداً"
            },
            "strikegpt-r1-zero-8b": {
                "role": "المبدع والمبتكر",
                "specialty": "الحلول الإبداعية والابتكار",
                "use_case": "حلول غير تقليدية، تحسينات إبداعية، أفكار جديدة",
                "speed": "متوسطة",
                "accuracy": "عالية"
            },
            "Qwen2.5-VL-7B": {
                "role": "المحلل البصري",
                "specialty": "تحليل الصور والمحتوى البصري",
                "use_case": "تحليل الواجهات، فهم الرسوم البيانية، التوثيق البصري",
                "speed": "متوسطة",
                "accuracy": "ممتازة"
            }
        }
        
        self.gemini_cli = {
            "role": "المنسق الرئيسي",
            "specialty": "التنسيق والإشراف العام",
            "use_case": "إدارة المشروع، التنسيق بين النماذج، المراجعة النهائية"
        }
    
    def check_available_models(self):
        """فحص النماذج المتاحة في Ollama"""
        try:
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                available_models = []
                lines = result.stdout.strip().split('\n')[1:]  # تجاهل العنوان
                for line in lines:
                    if line.strip():
                        model_name = line.split()[0]
                        available_models.append(model_name)
                
                print("🤖 النماذج المتاحة في Ollama:")
                for model in available_models:
                    if any(key in model for key in self.ai_models.keys()):
                        status = "✅ مختار للفريق"
                    else:
                        status = "⚪ متاح"
                    print(f"   {status} {model}")
                
                return available_models
            else:
                print("❌ خطأ في فحص Ollama")
                return []
        except Exception as e:
            print(f"❌ خطأ في الاتصال بـ Ollama: {e}")
            return []
    
    def create_collaboration_workflow(self):
        """إنشاء سير عمل التعاون"""
        workflow = {
            "project_phases": {
                "1_analysis": {
                    "name": "مرحلة التحليل",
                    "primary_model": "phi3:mini",
                    "support_models": ["mistral:7b"],
                    "tasks": [
                        "تحليل المتطلبات",
                        "فحص الكود الحالي", 
                        "تحديد المشاكل",
                        "اقتراح حلول سريعة"
                    ]
                },
                "2_planning": {
                    "name": "مرحلة التخطيط",
                    "primary_model": "llama3:8b",
                    "support_models": ["gemini_cli"],
                    "tasks": [
                        "وضع الاستراتيجية",
                        "تخطيط المراحل",
                        "تحديد الأولويات",
                        "توزيع المهام"
                    ]
                },
                "3_development": {
                    "name": "مرحلة التطوير",
                    "primary_model": "mistral:7b",
                    "support_models": ["phi3:mini", "strikegpt-r1-zero-8b"],
                    "tasks": [
                        "كتابة الكود",
                        "تطوير الميزات",
                        "حل المشاكل التقنية",
                        "تحسين الأداء"
                    ]
                },
                "4_innovation": {
                    "name": "مرحلة الابتكار",
                    "primary_model": "strikegpt-r1-zero-8b",
                    "support_models": ["llama3:8b", "Qwen2.5-VL-7B"],
                    "tasks": [
                        "اقتراح حلول إبداعية",
                        "تحسينات غير تقليدية",
                        "أفكار جديدة",
                        "تطوير مميز"
                    ]
                },
                "5_review": {
                    "name": "مرحلة المراجعة",
                    "primary_model": "gemini_cli",
                    "support_models": ["llama3:8b", "Qwen2.5-VL-7B"],
                    "tasks": [
                        "مراجعة شاملة",
                        "ضمان الجودة",
                        "التوثيق",
                        "الموافقة النهائية"
                    ]
                }
            }
        }
        
        return workflow
    
    def generate_collaboration_request(self, task_type, task_description):
        """إنشاء طلب تعاون للنماذج"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # تحديد النماذج المناسبة للمهمة
        if "analysis" in task_type.lower():
            primary_model = "phi3:mini"
            support_models = ["mistral:7b"]
        elif "planning" in task_type.lower():
            primary_model = "llama3:8b" 
            support_models = ["gemini_cli"]
        elif "development" in task_type.lower():
            primary_model = "mistral:7b"
            support_models = ["phi3:mini", "strikegpt-r1-zero-8b"]
        elif "innovation" in task_type.lower():
            primary_model = "strikegpt-r1-zero-8b"
            support_models = ["llama3:8b"]
        else:
            primary_model = "gemini_cli"
            support_models = ["llama3:8b"]
        
        request = {
            "task_id": f"anubis_task_{timestamp}",
            "task_type": task_type,
            "task_description": task_description,
            "primary_model": primary_model,
            "support_models": support_models,
            "workflow": {
                "step_1": f"🤖 {primary_model}: تحليل أولي وحل رئيسي",
                "step_2": f"🤝 {', '.join(support_models)}: مراجعة ودعم",
                "step_3": "🏺 Augment Agent: دمج النتائج وتنفيذ",
                "step_4": "📋 توثيق النتائج والتعلم"
            },
            "expected_output": "حل شامل ومدروس مع تبرير واضح",
            "timestamp": timestamp
        }
        
        return request
    
    def create_ollama_prompt_template(self, model_name, task_description):
        """إنشاء قالب prompt لنماذج Ollama"""
        model_info = self.ai_models.get(model_name, {})
        role = model_info.get("role", "مساعد")
        specialty = model_info.get("specialty", "عام")
        
        prompt = f"""
🏺 مشروع أنوبيس - Universal AI Assistants

🤖 دورك: {role}
🎯 تخصصك: {specialty}

📋 المهمة:
{task_description}

🔍 المطلوب منك:
1. تحليل المهمة من منظور تخصصك
2. اقتراح حل عملي ومفصل
3. تحديد المخاطر والتحديات
4. اقتراح خطوات التنفيذ

⚡ ملاحظات مهمة:
- هذا مشروع نظام مساعدين ذكيين
- نعمل مع Docker وPython وFastAPI
- الأمان والعزل مهمان جداً
- نفضل الحلول العملية والقابلة للتنفيذ

🎯 اجعل إجابتك:
- واضحة ومباشرة
- عملية وقابلة للتنفيذ
- مدعومة بالأمثلة
- مناسبة لدورك كـ{role}

---
الرجاء تقديم حلك:
"""
        return prompt
    
    def save_collaboration_plan(self):
        """حفظ خطة التعاون"""
        plan = {
            "anubis_ai_team": self.ai_models,
            "gemini_coordinator": self.gemini_cli,
            "collaboration_workflow": self.create_collaboration_workflow(),
            "usage_guidelines": {
                "quick_analysis": "استخدم phi3:mini للتحليل السريع",
                "complex_development": "استخدم mistral:7b للتطوير المعقد", 
                "strategic_planning": "استخدم llama3:8b للتخطيط الاستراتيجي",
                "creative_solutions": "استخدم strikegpt-r1-zero-8b للحلول الإبداعية",
                "visual_analysis": "استخدم Qwen2.5-VL-7B للتحليل البصري",
                "final_review": "استخدم Gemini CLI للمراجعة النهائية"
            },
            "created_at": datetime.now().isoformat()
        }
        
        with open("anubis_ai_team_collaboration_plan.json", "w", encoding="utf-8") as f:
            json.dump(plan, f, ensure_ascii=False, indent=2)
        
        print("💾 تم حفظ خطة التعاون: anubis_ai_team_collaboration_plan.json")
        return plan
    
    def display_team_info(self):
        """عرض معلومات الفريق"""
        print("🏺 فريق الذكاء الاصطناعي المساعد - مشروع أنوبيس")
        print("=" * 60)
        
        print("\n🤖 أعضاء الفريق:")
        for model_name, info in self.ai_models.items():
            print(f"\n📋 {model_name}")
            print(f"   🎭 الدور: {info['role']}")
            print(f"   🎯 التخصص: {info['specialty']}")
            print(f"   ⚡ السرعة: {info['speed']}")
            print(f"   🎯 الدقة: {info['accuracy']}")
            print(f"   💼 الاستخدام: {info['use_case']}")
        
        print(f"\n🌟 المنسق الرئيسي: Gemini CLI")
        print(f"   🎭 الدور: {self.gemini_cli['role']}")
        print(f"   🎯 التخصص: {self.gemini_cli['specialty']}")
        
        print("\n🔄 آلية العمل:")
        print("1. 🏺 Augment Agent يحدد نوع المهمة")
        print("2. 🤖 يختار النموذج الأنسب كقائد")
        print("3. 🤝 النماذج الداعمة تقدم المساعدة")
        print("4. 🌟 Gemini CLI يراجع ويوافق")
        print("5. 🏺 Augment Agent ينفذ الحل النهائي")

def main():
    """الدالة الرئيسية"""
    team = AnubisAITeam()
    
    print("🏺 إعداد فريق الذكاء الاصطناعي المساعد")
    print("=" * 50)
    
    # فحص النماذج المتاحة
    available_models = team.check_available_models()
    
    # عرض معلومات الفريق
    team.display_team_info()
    
    # حفظ خطة التعاون
    team.save_collaboration_plan()
    
    # مثال على طلب تعاون
    print("\n📋 مثال على طلب التعاون:")
    sample_request = team.generate_collaboration_request(
        "development", 
        "تحسين نظام العزل وإضافة ميزات جديدة"
    )
    print(json.dumps(sample_request, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
