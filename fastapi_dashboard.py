#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ واجهة FastAPI الجاهزة لأنوبيس وحورس
Ready-to-use FastAPI Interface for Anubis and Horus

واجهة ويب سريعة ومتقدمة مع API شامل
Fast and advanced web interface with comprehensive API
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

class AnubisHorusFastAPIApp:
    """⚡ تطبيق FastAPI لأنوبيس وحورس"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.app = FastAPI(
            title="🏺 نظام أنوبيس & 𓅃 فريق حورس",
            description="واجهة ويب شاملة للذكاء الاصطناعي المتكامل",
            version="2.0.0",
            docs_url="/api/docs",
            redoc_url="/api/redoc"
        )
        
        self.project_root = Path(__file__).parent
        self.setup_routes()
        self.setup_static_files()
    
    def setup_static_files(self):
        """إعداد الملفات الثابتة"""
        static_dir = self.project_root / "static"
        static_dir.mkdir(exist_ok=True)
        
        templates_dir = self.project_root / "templates"
        templates_dir.mkdir(exist_ok=True)
        
        # إنشاء ملف HTML أساسي
        self.create_html_template(templates_dir)
        self.create_css_file(static_dir)
        self.create_js_file(static_dir)
        
        self.app.mount("/static", StaticFiles(directory=static_dir), name="static")
        self.templates = Jinja2Templates(directory=templates_dir)
    
    def create_html_template(self, templates_dir):
        """إنشاء قالب HTML"""
        html_content = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏺 أنوبيس & 𓅃 حورس - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-ankh"></i> أنوبيس & 𓅃 حورس
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-circle text-success"></i> النظام نشط
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action active" onclick="showSection('dashboard')">
                                <i class="fas fa-home"></i> الرئيسية
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="showSection('horus')">
                                <i class="fas fa-users"></i> فريق حورس
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="showSection('api-keys')">
                                <i class="fas fa-key"></i> مفاتيح API
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="showSection('system')">
                                <i class="fas fa-cog"></i> النظام
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9">
                <!-- قسم الرئيسية -->
                <div id="dashboard-section" class="content-section">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-white bg-success">
                                <div class="card-body">
                                    <h5 class="card-title">🔑 مفاتيح API</h5>
                                    <h2 id="api-keys-count">726</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-info">
                                <div class="card-body">
                                    <h5 class="card-title">🤖 النماذج</h5>
                                    <h2 id="models-count">8</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-warning">
                                <div class="card-body">
                                    <h5 class="card-title">📈 الطلبات</h5>
                                    <h2 id="requests-count">15,420</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-danger">
                                <div class="card-body">
                                    <h5 class="card-title">✅ النجاح</h5>
                                    <h2 id="success-rate">98.5%</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>📊 حالة الأنظمة</h5>
                                </div>
                                <div class="card-body">
                                    <div id="system-status"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>📈 الإحصائيات</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="statsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم فريق حورس -->
                <div id="horus-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5>𓅃 فريق حورس - الوكلاء الأذكياء</h5>
                        </div>
                        <div class="card-body">
                            <div id="horus-team"></div>
                        </div>
                    </div>
                </div>

                <!-- قسم مفاتيح API -->
                <div id="api-keys-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5>🔐 إدارة مفاتيح API</h5>
                        </div>
                        <div class="card-body">
                            <div id="api-keys-management"></div>
                        </div>
                    </div>
                </div>

                <!-- قسم النظام -->
                <div id="system-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5>⚙️ إعدادات النظام</h5>
                        </div>
                        <div class="card-body">
                            <div id="system-settings"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/script.js"></script>
</body>
</html>'''
        
        with open(templates_dir / "index.html", "w", encoding="utf-8") as f:
            f.write(html_content)
    
    def create_css_file(self, static_dir):
        """إنشاء ملف CSS"""
        css_content = '''
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px 15px 0 0 !important;
}

.list-group-item.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-color: #667eea;
}

.content-section {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-active { background-color: #28a745; }
.status-warning { background-color: #ffc107; }
.status-error { background-color: #dc3545; }
'''
        
        with open(static_dir / "style.css", "w", encoding="utf-8") as f:
            f.write(css_content)
    
    def create_js_file(self, static_dir):
        """إنشاء ملف JavaScript"""
        js_content = '''
// إظهار القسم المحدد
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.content-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // إظهار القسم المحدد
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // تحديث الشريط الجانبي
    document.querySelectorAll('.list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.classList.add('active');
}

// تحميل البيانات
async function loadData() {
    try {
        // تحميل حالة النظام
        const systemResponse = await fetch('/api/system/status');
        const systemData = await systemResponse.json();
        updateSystemStatus(systemData);
        
        // تحميل فريق حورس
        const horusResponse = await fetch('/api/horus/team');
        const horusData = await horusResponse.json();
        updateHorusTeam(horusData);
        
        // تحميل مفاتيح API
        const keysResponse = await fetch('/api/keys/status');
        const keysData = await keysResponse.json();
        updateAPIKeys(keysData);
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
    }
}

// تحديث حالة النظام
function updateSystemStatus(data) {
    const statusDiv = document.getElementById('system-status');
    let html = '';
    
    for (const [component, status] of Object.entries(data)) {
        const statusClass = status.includes('نشط') ? 'status-active' : 
                           status.includes('تحذير') ? 'status-warning' : 'status-error';
        
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>${component}</span>
                <span>
                    <span class="status-indicator ${statusClass}"></span>
                    ${status}
                </span>
            </div>
        `;
    }
    
    statusDiv.innerHTML = html;
}

// تحديث فريق حورس
function updateHorusTeam(data) {
    const teamDiv = document.getElementById('horus-team');
    let html = '<div class="row">';
    
    data.agents.forEach(agent => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">${agent.name}</h6>
                        <p class="card-text">${agent.role}</p>
                        <small class="text-muted">${agent.model}</small>
                        <span class="status-indicator status-active float-end"></span>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    teamDiv.innerHTML = html;
}

// تحديث مفاتيح API
function updateAPIKeys(data) {
    const keysDiv = document.getElementById('api-keys-management');
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>المنصة</th><th>المفاتيح</th><th>الحالة</th><th>آخر استخدام</th></tr></thead><tbody>';
    
    data.platforms.forEach(platform => {
        html += `
            <tr>
                <td>${platform.name}</td>
                <td>${platform.keys_count}</td>
                <td><span class="status-indicator status-active"></span> ${platform.status}</td>
                <td>${platform.last_used}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    keysDiv.innerHTML = html;
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    
    // تحديث البيانات كل 30 ثانية
    setInterval(loadData, 30000);
});
'''
        
        with open(static_dir / "script.js", "w", encoding="utf-8") as f:
            f.write(js_content)
    
    def setup_routes(self):
        """إعداد المسارات"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard(request: Request):
            """الصفحة الرئيسية"""
            return self.templates.TemplateResponse("index.html", {"request": request})
        
        @self.app.get("/api/system/status")
        async def system_status():
            """حالة النظام"""
            return {
                "🏺 نظام أنوبيس": "🟢 نشط ويعمل بكفاءة عالية",
                "𓅃 فريق حورس": "🟢 جميع الوكلاء متصلون",
                "🔗 خادم MCP": "🟢 يعمل على المنفذ 3000",
                "🔐 مفاتيح API": "🟢 726 مفتاح آمن",
                "📊 الأداء": "🟢 معدل نجاح 98.5%"
            }
        
        @self.app.get("/api/horus/team")
        async def horus_team():
            """معلومات فريق حورس"""
            return {
                "agents": [
                    {"name": "⚡ THOTH", "role": "المحلل السريع", "model": "phi3:mini", "status": "نشط"},
                    {"name": "🔧 PTAH", "role": "المطور الخبير", "model": "mistral:7b", "status": "نشط"},
                    {"name": "🎯 RA", "role": "المستشار الاستراتيجي", "model": "llama3:8b", "status": "نشط"},
                    {"name": "💡 KHNUM", "role": "المبدع والمبتكر", "model": "strikegpt-r1-zero-8b", "status": "نشط"},
                    {"name": "👁️ SESHAT", "role": "المحللة البصرية", "model": "Qwen2.5-VL-7B", "status": "نشط"},
                    {"name": "𓅃 HORUS", "role": "المنسق الأعلى", "model": "Gemini CLI", "status": "نشط"}
                ]
            }
        
        @self.app.get("/api/keys/status")
        async def api_keys_status():
            """حالة مفاتيح API"""
            return {
                "platforms": [
                    {"name": "OpenAI", "keys_count": 11, "status": "نشط", "last_used": "منذ 5 دقائق"},
                    {"name": "Anthropic", "keys_count": 1, "status": "نشط", "last_used": "منذ 10 دقائق"},
                    {"name": "Google Gemini", "keys_count": 10, "status": "نشط", "last_used": "منذ 2 دقائق"},
                    {"name": "Mistral", "keys_count": 162, "status": "نشط", "last_used": "منذ 1 دقيقة"},
                    {"name": "DeepSeek", "keys_count": 6, "status": "نشط", "last_used": "منذ 15 دقيقة"}
                ]
            }
    
    def run(self, host="0.0.0.0", port=8080, **kwargs):
        """تشغيل التطبيق"""
        print("🚀 تشغيل واجهة FastAPI...")
        print(f"🌐 الرابط: http://localhost:{port}")
        print(f"📚 API Docs: http://localhost:{port}/api/docs")
        
        uvicorn.run(self.app, host=host, port=port, **kwargs)

# تشغيل التطبيق
if __name__ == "__main__":
    app = AnubisHorusFastAPIApp()
    app.run()
