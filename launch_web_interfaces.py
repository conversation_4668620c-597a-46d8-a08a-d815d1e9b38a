#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 مشغل الواجهات الجاهزة لأنوبيس وحورس
Ready-to-use Web Interfaces Launcher for Anubis and Horus

تشغيل سريع لجميع الواجهات المتاحة
Quick launch for all available interfaces
"""

import os
import sys
import subprocess
import time
from pathlib import Path

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.prompt import Prompt
except ImportError:
    print("⚠️ تثبيت المكتبات المطلوبة...")
    os.system("pip install rich")
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.prompt import Prompt

console = Console()

class WebInterfacesLauncher:
    """🚀 مشغل الواجهات الجاهزة"""
    
    def __init__(self):
        """تهيئة المشغل"""
        self.project_root = Path(__file__).parent
        self.interfaces = {
            "1": {
                "name": "🌟 Streamlit Dashboard",
                "description": "واجهة سهلة وسريعة مع رسوم بيانية تفاعلية",
                "file": "streamlit_dashboard.py",
                "port": 8501,
                "command": ["streamlit", "run", "streamlit_dashboard.py", "--server.port=8501"],
                "install": ["streamlit", "plotly", "pandas"],
                "pros": ["سهل الاستخدام", "رسوم بيانية جميلة", "تحديث تلقائي"],
                "best_for": "المبتدئين والعرض السريع"
            },
            "2": {
                "name": "🎨 Gradio Interface",
                "description": "واجهة ذكاء اصطناعي تفاعلية مع محادثة",
                "file": "gradio_interface.py",
                "port": 7860,
                "command": ["python", "gradio_interface.py"],
                "install": ["gradio"],
                "pros": ["محادثة تفاعلية", "سهل التخصيص", "مشاركة سحابية"],
                "best_for": "التفاعل مع الذكاء الاصطناعي"
            },
            "3": {
                "name": "⚡ FastAPI Dashboard",
                "description": "واجهة متقدمة مع API شامل",
                "file": "fastapi_dashboard.py",
                "port": 8080,
                "command": ["python", "fastapi_dashboard.py"],
                "install": ["fastapi", "uvicorn", "jinja2"],
                "pros": ["أداء عالي", "API متكامل", "تخصيص كامل"],
                "best_for": "التطبيقات المتقدمة والإنتاج"
            },
            "4": {
                "name": "📊 Visual Dashboard (موجود)",
                "description": "لوحة التحكم المرئية الموجودة في النظام",
                "file": "ANUBIS_HORUS_MCP/api_keys_vault/visual_dashboard_system.py",
                "port": 5000,
                "command": ["python", "ANUBIS_HORUS_MCP/api_keys_vault/visual_dashboard_system.py"],
                "install": ["flask", "flask-cors"],
                "pros": ["متكامل مع النظام", "إدارة مفاتيح", "أمان متقدم"],
                "best_for": "إدارة النظام الحالي"
            }
        }
    
    def show_header(self):
        """عرض الرأس الرئيسي"""
        console.print(Panel.fit(
            "[bold blue]🚀 مشغل الواجهات الجاهزة لأنوبيس وحورس[/bold blue]\n"
            "[dim]اختر الواجهة المناسبة لاحتياجاتك[/dim]",
            border_style="blue"
        ))
    
    def show_interfaces_table(self):
        """عرض جدول الواجهات"""
        table = Table(title="🌐 الواجهات المتاحة", show_header=True, header_style="bold magenta")
        
        table.add_column("الرقم", style="dim", width=6)
        table.add_column("الاسم", style="bold")
        table.add_column("الوصف", style="dim")
        table.add_column("المنفذ", justify="center")
        table.add_column("الأفضل لـ", style="green")
        
        for key, interface in self.interfaces.items():
            table.add_row(
                key,
                interface["name"],
                interface["description"],
                str(interface["port"]),
                interface["best_for"]
            )
        
        console.print(table)
    
    def show_interface_details(self, interface_key):
        """عرض تفاصيل الواجهة"""
        interface = self.interfaces[interface_key]
        
        console.print(f"\n[bold]{interface['name']}[/bold]")
        console.print(f"📝 الوصف: {interface['description']}")
        console.print(f"🌐 المنفذ: {interface['port']}")
        console.print(f"🎯 الأفضل لـ: {interface['best_for']}")
        
        console.print("\n✨ المميزات:")
        for pro in interface["pros"]:
            console.print(f"  • {pro}")
        
        console.print(f"\n📦 المكتبات المطلوبة: {', '.join(interface['install'])}")
    
    def install_requirements(self, packages):
        """تثبيت المتطلبات"""
        console.print(f"\n📦 تثبيت المكتبات المطلوبة...")
        
        for package in packages:
            try:
                console.print(f"⏳ تثبيت {package}...")
                result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    console.print(f"✅ تم تثبيت {package} بنجاح")
                else:
                    console.print(f"⚠️ خطأ في تثبيت {package}: {result.stderr}")
            except Exception as e:
                console.print(f"❌ خطأ في تثبيت {package}: {e}")
    
    def launch_interface(self, interface_key):
        """تشغيل الواجهة"""
        interface = self.interfaces[interface_key]
        
        console.print(f"\n🚀 تشغيل {interface['name']}...")
        
        # تثبيت المتطلبات
        self.install_requirements(interface["install"])
        
        # التحقق من وجود الملف
        file_path = self.project_root / interface["file"]
        if not file_path.exists():
            console.print(f"❌ الملف غير موجود: {interface['file']}")
            return False
        
        try:
            console.print(f"🌐 الرابط: http://localhost:{interface['port']}")
            console.print("⏳ جاري التشغيل...")
            
            # تشغيل الواجهة
            process = subprocess.Popen(
                interface["command"],
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # انتظار قصير للتأكد من التشغيل
            time.sleep(3)
            
            if process.poll() is None:
                console.print(f"✅ تم تشغيل {interface['name']} بنجاح!")
                console.print(f"🌐 افتح المتصفح على: http://localhost:{interface['port']}")
                console.print("⚠️ اضغط Ctrl+C لإيقاف الخادم")
                
                # انتظار إيقاف المستخدم
                try:
                    process.wait()
                except KeyboardInterrupt:
                    console.print("\n🛑 إيقاف الخادم...")
                    process.terminate()
                    console.print("✅ تم إيقاف الخادم بنجاح")
                
                return True
            else:
                stdout, stderr = process.communicate()
                console.print(f"❌ فشل في تشغيل الواجهة")
                console.print(f"خطأ: {stderr}")
                return False
                
        except Exception as e:
            console.print(f"❌ خطأ في تشغيل الواجهة: {e}")
            return False
    
    def show_quick_comparison(self):
        """عرض مقارنة سريعة"""
        console.print("\n📊 مقارنة سريعة:")
        
        comparison_table = Table(show_header=True, header_style="bold cyan")
        comparison_table.add_column("الواجهة")
        comparison_table.add_column("السهولة")
        comparison_table.add_column("الميزات")
        comparison_table.add_column("الأداء")
        comparison_table.add_column("التخصيص")
        
        comparison_table.add_row("Streamlit", "⭐⭐⭐⭐⭐", "⭐⭐⭐⭐", "⭐⭐⭐", "⭐⭐⭐")
        comparison_table.add_row("Gradio", "⭐⭐⭐⭐⭐", "⭐⭐⭐⭐⭐", "⭐⭐⭐", "⭐⭐⭐⭐")
        comparison_table.add_row("FastAPI", "⭐⭐⭐", "⭐⭐⭐⭐⭐", "⭐⭐⭐⭐⭐", "⭐⭐⭐⭐⭐")
        comparison_table.add_row("Visual Dashboard", "⭐⭐⭐⭐", "⭐⭐⭐⭐⭐", "⭐⭐⭐⭐", "⭐⭐⭐⭐")
        
        console.print(comparison_table)
    
    def run(self):
        """تشغيل المشغل"""
        self.show_header()
        
        while True:
            self.show_interfaces_table()
            self.show_quick_comparison()
            
            console.print("\n🎛️ الخيارات:")
            console.print("1-4: تشغيل الواجهة المحددة")
            console.print("i: عرض تفاصيل واجهة")
            console.print("q: خروج")
            
            choice = Prompt.ask("\nاختر الخيار", default="q")
            
            if choice.lower() == 'q':
                console.print("👋 وداعاً!")
                break
            elif choice.lower() == 'i':
                interface_key = Prompt.ask("أدخل رقم الواجهة لعرض التفاصيل", choices=list(self.interfaces.keys()))
                self.show_interface_details(interface_key)
                input("\nاضغط Enter للمتابعة...")
            elif choice in self.interfaces:
                self.show_interface_details(choice)
                
                confirm = Prompt.ask(f"\nهل تريد تشغيل هذه الواجهة؟", choices=["y", "n"], default="y")
                if confirm.lower() == 'y':
                    self.launch_interface(choice)
            else:
                console.print("❌ خيار غير صحيح")

# تشغيل المشغل
if __name__ == "__main__":
    launcher = WebInterfacesLauncher()
    launcher.run()
