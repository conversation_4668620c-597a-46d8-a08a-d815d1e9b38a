
// إظهار القسم المحدد
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.content-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // إظهار القسم المحدد
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // تحديث الشريط الجانبي
    document.querySelectorAll('.list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.classList.add('active');
}

// تحميل البيانات
async function loadData() {
    try {
        // تحميل حالة النظام
        const systemResponse = await fetch('/api/system/status');
        const systemData = await systemResponse.json();
        updateSystemStatus(systemData);
        
        // تحميل فريق حورس
        const horusResponse = await fetch('/api/horus/team');
        const horusData = await horusResponse.json();
        updateHorusTeam(horusData);
        
        // تحميل مفاتيح API
        const keysResponse = await fetch('/api/keys/status');
        const keysData = await keysResponse.json();
        updateAPIKeys(keysData);
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
    }
}

// تحديث حالة النظام
function updateSystemStatus(data) {
    const statusDiv = document.getElementById('system-status');
    let html = '';
    
    for (const [component, status] of Object.entries(data)) {
        const statusClass = status.includes('نشط') ? 'status-active' : 
                           status.includes('تحذير') ? 'status-warning' : 'status-error';
        
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>${component}</span>
                <span>
                    <span class="status-indicator ${statusClass}"></span>
                    ${status}
                </span>
            </div>
        `;
    }
    
    statusDiv.innerHTML = html;
}

// تحديث فريق حورس
function updateHorusTeam(data) {
    const teamDiv = document.getElementById('horus-team');
    let html = '<div class="row">';
    
    data.agents.forEach(agent => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">${agent.name}</h6>
                        <p class="card-text">${agent.role}</p>
                        <small class="text-muted">${agent.model}</small>
                        <span class="status-indicator status-active float-end"></span>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    teamDiv.innerHTML = html;
}

// تحديث مفاتيح API
function updateAPIKeys(data) {
    const keysDiv = document.getElementById('api-keys-management');
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>المنصة</th><th>المفاتيح</th><th>الحالة</th><th>آخر استخدام</th></tr></thead><tbody>';
    
    data.platforms.forEach(platform => {
        html += `
            <tr>
                <td>${platform.name}</td>
                <td>${platform.keys_count}</td>
                <td><span class="status-indicator status-active"></span> ${platform.status}</td>
                <td>${platform.last_used}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    keysDiv.innerHTML = html;
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    
    // تحديث البيانات كل 30 ثانية
    setInterval(loadData, 30000);
});
